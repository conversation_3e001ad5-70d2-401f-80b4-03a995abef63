import { useEffect, useRef } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';

/**
 * Custom hook for managing noise suppression on audio tracks
 * @param {Object} room - LiveKit room instance
 * @param {boolean} noiseSuppressionEnabled - Whether noise suppression should be enabled
 * @param {function} setToastNotification - Function to show toast notifications
 * @param {function} setToastStatus - Function to set toast status
 * @param {function} setShowToast - Function to show/hide toast
 * @returns {Object} - Hook state and methods
 */
export const useNoiseSuppression = (
  room,
  noiseSuppressionEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
) => {
  // Refs for managing noise suppression state
  const noiseProcessorRef = useRef(null);
  const originalTrackRef = useRef(null);
  const isProcessingRef = useRef(false);
  const processedTrackRef = useRef(null);
  const isNoiseSuppressionActiveRef = useRef(false);

  useEffect(() => {
    const applyNoiseSuppression = async () => {
      if (!room || room.state !== "connected") return;

      // Prevent multiple simultaneous processing attempts
      if (isProcessingRef.current) {
        console.log("Noise suppression processing already in progress");
        return;
      }

      // Check if we're already in the desired state
      if (noiseSuppressionEnabled === isNoiseSuppressionActiveRef.current) {
        console.log(`Noise suppression already in desired state: ${noiseSuppressionEnabled}`);
        return;
      }

      try {
        isProcessingRef.current = true;

        // Get the local audio track publication
        const audioTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
        if (!audioTrackPublication || !audioTrackPublication.track) {
          console.log("No audio track found for noise suppression");
          return;
        }

        const localAudioTrack = audioTrackPublication.track;

        if (noiseSuppressionEnabled && !isNoiseSuppressionActiveRef.current) {
          console.log("Enabling noise suppression...");

          // Store the original track before processing (clone it to preserve)
          if (!originalTrackRef.current) {
            originalTrackRef.current = localAudioTrack.mediaStreamTrack.clone();
            console.log("Original track stored");
          }

          // Create noise suppression processor
          noiseProcessorRef.current = new NoiseSuppressionProcessor();

          // Start processing the audio track
          const processedTrack = await noiseProcessorRef.current.startProcessing(
            localAudioTrack.mediaStreamTrack
          );

          if (processedTrack) {
            // Store the processed track reference
            processedTrackRef.current = processedTrack;

            // Replace the original track with the processed one
            await localAudioTrack.replaceTrack(processedTrack, true);

            // Mark noise suppression as active
            isNoiseSuppressionActiveRef.current = true;

            console.log("✅ Noise suppression enabled successfully");

            // Show success notification
            if (setToastNotification && setToastStatus && setShowToast) {
              setToastNotification("Noise suppression enabled");
              setToastStatus("success");
              setShowToast(true);
            }
          }
        } else if (!noiseSuppressionEnabled && isNoiseSuppressionActiveRef.current) {
          console.log("Disabling noise suppression...");

          try {
            // Stop the noise processor first
            if (noiseProcessorRef.current) {
              await noiseProcessorRef.current.stopProcessing();
              noiseProcessorRef.current = null;
            }
            processedTrackRef.current = null;

            // Create a fresh audio track to replace the processed one
            console.log("Creating fresh audio track...");
            const currentSettings = localAudioTrack.mediaStreamTrack.getSettings();
            const constraints = {
              audio: {
                deviceId: currentSettings.deviceId || 'default',
                echoCancellation: true,
                noiseSuppression: false, // Disable browser noise suppression
                autoGainControl: true
              }
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            const newAudioTrack = stream.getAudioTracks()[0];

            if (newAudioTrack) {
              await localAudioTrack.replaceTrack(newAudioTrack, true);
              console.log("✅ Fresh audio track created and applied");
            }

            // Clear references
            if (originalTrackRef.current) {
              originalTrackRef.current.stop();
              originalTrackRef.current = null;
            }

            // Mark noise suppression as inactive
            isNoiseSuppressionActiveRef.current = false;

            console.log("✅ Noise suppression disabled successfully");

            // Show notification
            if (setToastNotification && setToastStatus && setShowToast) {
              setToastNotification("Noise suppression disabled");
              setToastStatus("info");
              setShowToast(true);
            }
          } catch (restoreError) {
            console.error("❌ Failed to restore audio track:", restoreError);
            if (setToastNotification && setToastStatus && setShowToast) {
              setToastNotification("Failed to restore audio");
              setToastStatus("error");
              setShowToast(true);
            }
          }
        }
      } catch (error) {
        console.error("❌ Failed to apply/remove noise suppression:", error);
        if (setToastNotification && setToastStatus && setShowToast) {
          setToastNotification("Failed to update noise suppression");
          setToastStatus("error");
          setShowToast(true);
        }
      } finally {
        isProcessingRef.current = false;
      }
    };

    // Only run when room is connected and we need to change state
    if (room && room.state === "connected") {
      // Small delay to ensure audio track is fully initialized
      setTimeout(applyNoiseSuppression, 500);
    }

    // No cleanup function - let the state persist
  }, [room, room?.state, noiseSuppressionEnabled, setToastNotification, setToastStatus, setShowToast]);

  // Separate cleanup effect for when component unmounts
  useEffect(() => {
    return () => {
      console.log("useNoiseSuppression cleanup - cleaning up noise suppression");
      if (noiseProcessorRef.current) {
        try {
          noiseProcessorRef.current.stopProcessing();
          noiseProcessorRef.current = null;
        } catch (error) {
          console.error("Error cleaning up noise processor:", error);
        }
      }

      // Clean up all references
      if (originalTrackRef.current) {
        originalTrackRef.current.stop();
        originalTrackRef.current = null;
      }

      if (processedTrackRef.current) {
        processedTrackRef.current = null;
      }

      isProcessingRef.current = false;
      isNoiseSuppressionActiveRef.current = false;
    };
  }, []);

  // Return hook state and methods
  return {
    isNoiseSuppressionActive: isNoiseSuppressionActiveRef.current,
    isProcessing: isProcessingRef.current,
  };
};
